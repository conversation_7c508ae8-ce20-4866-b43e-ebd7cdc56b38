import os
import shutil
import torch
import gc
from fastapi import FastAPI, UploadFile, File, Form
from fastapi.responses import FileResponse
from tempfile import NamedTemporaryFile
import time
import httpx
import logging
from huggingface_hub import snapshot_download
from models.vc.vevo.vevo_utils import VevoInferencePipeline, save_audio
from pydub import AudioSegment
from pydub.effects import normalize
import soundfile as sf
from df.enhance import init_df, enhance
from fastapi import FastAPI, HTTPException, Response
from pydantic import BaseModel
from kokoro import KPipeline
import soundfile as sf
import io
import math
import numpy as np
from typing import Optional


torch.cuda.empty_cache()
app = FastAPI(title="Vevo API")

# ===== Logger Setup =====
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ===== Chatterbox API Configuration =====
EXISTING_API_URL_CH = "http://*************:8002"
TIMEOUT = 300  # 5 minutes timeout

# ===== Device =====
device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

# Global variables for lazy loading
timbre_pipeline = None
tts_pipeline = None
style_pipeline = None
denoise_model = None
denoise_df_state = None

# ===== Model Paths Setup (Download but don't load models yet) =====
def setup_model_paths():
    """Download model files and setup paths without loading models"""
    logger.info("🔧 Setting up model paths and downloading required files...")
    tokenizer_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["tokenizer/vq8192/*"],
    )
    tokenizer_ckpt_path = os.path.join(tokenizer_dir, "tokenizer/vq8192")

    fmt_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["acoustic_modeling/Vq8192ToMels/*"],
    )
    fmt_cfg_path = "./models/vc/vevo/config/Vq8192ToMels.json"
    fmt_ckpt_path = os.path.join(fmt_dir, "acoustic_modeling/Vq8192ToMels")

    vocoder_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["acoustic_modeling/Vocoder/*"],
    )
    vocoder_cfg_path = "./models/vc/vevo/config/Vocoder.json"
    vocoder_ckpt_path = os.path.join(vocoder_dir, "acoustic_modeling/Vocoder")

    # TTS Model Setup
    ar_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["contentstyle_modeling/PhoneToVq8192/*"],
    )
    ar_cfg_path = "./models/vc/vevo/config/PhoneToVq8192.json"
    ar_ckpt_path = os.path.join(ar_dir, "contentstyle_modeling/PhoneToVq8192")

    # Style Transfer Model Setup (Content Tokenizer)
    content_tokenizer_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["tokenizer/vq32/*"],
    )
    content_tokenizer_ckpt_path = os.path.join(
        content_tokenizer_dir, "tokenizer/vq32/hubert_large_l18_c32.pkl"
    )

    # Style Transfer AR Model Setup
    style_ar_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["contentstyle_modeling/Vq32ToVq8192/*"],
    )
    style_ar_cfg_path = "./models/vc/vevo/config/Vq32ToVq8192.json"
    style_ar_ckpt_path = os.path.join(style_ar_dir, "contentstyle_modeling/Vq32ToVq8192")

    logger.info("✅ Model paths setup completed successfully")
    return {
        'tokenizer_ckpt_path': tokenizer_ckpt_path,
        'fmt_cfg_path': fmt_cfg_path,
        'fmt_ckpt_path': fmt_ckpt_path,
        'vocoder_cfg_path': vocoder_cfg_path,
        'vocoder_ckpt_path': vocoder_ckpt_path,
        'ar_cfg_path': ar_cfg_path,
        'ar_ckpt_path': ar_ckpt_path,
        'content_tokenizer_ckpt_path': content_tokenizer_ckpt_path,
        'style_ar_cfg_path': style_ar_cfg_path,
        'style_ar_ckpt_path': style_ar_ckpt_path,
    }

# Setup model paths
model_paths = setup_model_paths()

logger.info("🚀 Vevo FastAPI Server initialized successfully!")
logger.info(f"🖥️ Device: {device}")
logger.info(f"🌐 Chatterbox API URL: {EXISTING_API_URL_CH}")
logger.info("📋 Available endpoints:")
logger.info("  - POST /timbre-transfer/ - Transfer timbre between audio files (with optional normalization and reference audio noise cancellation)")
logger.info("  - POST /style-transfer/ - Transfer speaking style between audio files")
logger.info("  - POST /text-to-speech/ - Generate speech from text with optional noise cancellation and Chatterbox processing")
logger.info("  - POST /tts - Generate speech from text using Kokoro TTS")
logger.info("  - POST /combined-pipeline/ - Combined Kokoro TTS + Style Transfer + Timbre Transfer pipeline")


# ===== Noise Cancellation Functions =====
def get_denoise_model():
    """Lazy load DeepFilterNet model"""
    global denoise_model, denoise_df_state
    if denoise_model is None:
        logger.info("🔧 Loading DeepFilterNet model for noise cancellation...")
        denoise_model, denoise_df_state, _ = init_df()
        logger.info("✅ DeepFilterNet model loaded successfully")
    return denoise_model, denoise_df_state


def convert_to_wav(input_path, temp_wav="temp_input.wav"):
    """Convert audio file to WAV format with proper settings"""
    logger.info(f"🔄 Converting audio to WAV format: {input_path} -> {temp_wav}")
    audio = AudioSegment.from_file(input_path)
    audio = audio.set_channels(1).set_frame_rate(48000)
    audio.export(temp_wav, format="wav")
    logger.info(f"✅ Audio converted to WAV successfully: {temp_wav}")
    return temp_wav


def load_wav_tensor(wav_path, sr=48000):
    """Load WAV file as tensor"""
    data, file_sr = sf.read(wav_path)
    if file_sr != sr:
        raise ValueError(f"Sample rate is {file_sr}, expected {sr}")
    tensor = torch.from_numpy(data).float().unsqueeze(0)
    return tensor, sr


def denoise_tensor(input_tensor, model, df_state):
    """Apply noise cancellation to audio tensor"""
    input_tensor = input_tensor.cpu()  # Ensure tensor is on CPU
    with torch.no_grad():
        output = enhance(model, df_state, input_tensor)
    return output.squeeze(0).numpy()


def apply_noise_cancellation(input_audio_path, output_audio_path):
    """Apply noise cancellation to audio file"""
    logger.info(f"🔊 Starting noise cancellation: {input_audio_path}")
    temp_wav = convert_to_wav(input_audio_path)
    model, df_state = get_denoise_model()
    logger.info("🔧 Loading audio tensor for noise cancellation...")
    tensor, sr = load_wav_tensor(temp_wav, sr=df_state.sr())
    logger.info("🔧 Applying noise cancellation to audio...")
    enhanced = denoise_tensor(tensor, model, df_state)
    sf.write(output_audio_path, enhanced, sr)
    logger.info(f"✅ Noise cancellation completed: {output_audio_path}")
    os.remove(temp_wav)
    return output_audio_path


def normalize_audio(input_audio_path, output_audio_path):
    """Apply audio normalization to audio file"""
    logger.info(f"🔧 Starting audio normalization: {input_audio_path}")
    audio = AudioSegment.from_file(input_audio_path)
    normalized_audio = normalize(audio)
    normalized_audio.export(output_audio_path, format="wav")
    logger.info(f"✅ Audio normalization completed: {output_audio_path}")
    return output_audio_path


def get_timbre_pipeline():
    """Lazy load timbre transfer pipeline"""
    global timbre_pipeline
    if timbre_pipeline is None:
        logger.info("🔧 Loading timbre transfer pipeline...")
        # Clear GPU cache before loading
        torch.cuda.empty_cache()
        gc.collect()
        logger.info("🧹 GPU cache cleared before loading timbre pipeline")

        timbre_pipeline = VevoInferencePipeline(
            content_style_tokenizer_ckpt_path=model_paths['tokenizer_ckpt_path'],
            fmt_cfg_path=model_paths['fmt_cfg_path'],
            fmt_ckpt_path=model_paths['fmt_ckpt_path'],
            vocoder_cfg_path=model_paths['vocoder_cfg_path'],
            vocoder_ckpt_path=model_paths['vocoder_ckpt_path'],
            device=device,
        )
        logger.info("✅ Timbre transfer pipeline loaded successfully")
    return timbre_pipeline


def get_tts_pipeline():
    """Lazy load TTS pipeline"""
    global tts_pipeline
    if tts_pipeline is None:
        logger.info("🔧 Loading TTS pipeline...")
        # Clear GPU cache before loading
        torch.cuda.empty_cache()
        gc.collect()
        logger.info("🧹 GPU cache cleared before loading TTS pipeline")

        tts_pipeline = VevoInferencePipeline(
            content_style_tokenizer_ckpt_path=model_paths['tokenizer_ckpt_path'],
            ar_cfg_path=model_paths['ar_cfg_path'],
            ar_ckpt_path=model_paths['ar_ckpt_path'],
            fmt_cfg_path=model_paths['fmt_cfg_path'],
            fmt_ckpt_path=model_paths['fmt_ckpt_path'],
            vocoder_cfg_path=model_paths['vocoder_cfg_path'],
            vocoder_ckpt_path=model_paths['vocoder_ckpt_path'],
            device=device,
        )
        logger.info("✅ TTS pipeline loaded successfully")
    return tts_pipeline


def get_style_pipeline():
    """Lazy load style transfer pipeline"""
    global style_pipeline
    if style_pipeline is None:
        logger.info("🔧 Loading style transfer pipeline...")
        # Clear GPU cache before loading
        torch.cuda.empty_cache()
        gc.collect()
        logger.info("🧹 GPU cache cleared before loading style pipeline")

        style_pipeline = VevoInferencePipeline(
            content_tokenizer_ckpt_path=model_paths['content_tokenizer_ckpt_path'],
            content_style_tokenizer_ckpt_path=model_paths['tokenizer_ckpt_path'],
            ar_cfg_path=model_paths['style_ar_cfg_path'],
            ar_ckpt_path=model_paths['style_ar_ckpt_path'],
            fmt_cfg_path=model_paths['fmt_cfg_path'],
            fmt_ckpt_path=model_paths['fmt_ckpt_path'],
            vocoder_cfg_path=model_paths['vocoder_cfg_path'],
            vocoder_ckpt_path=model_paths['vocoder_ckpt_path'],
            device=device,
        )
        logger.info("✅ Style transfer pipeline loaded successfully")
    return style_pipeline


def clear_unused_pipeline(keep_pipeline):
    """Clear the pipeline that's not being used to free GPU memory"""
    global timbre_pipeline, tts_pipeline, style_pipeline

    if keep_pipeline == "timbre":
        if tts_pipeline is not None:
            logger.info("🧹 Clearing TTS pipeline to free GPU memory for timbre transfer")
            del tts_pipeline
            tts_pipeline = None
        if style_pipeline is not None:
            logger.info("🧹 Clearing style pipeline to free GPU memory for timbre transfer")
            del style_pipeline
            style_pipeline = None
    elif keep_pipeline == "tts":
        if timbre_pipeline is not None:
            logger.info("🧹 Clearing timbre pipeline to free GPU memory for TTS")
            del timbre_pipeline
            timbre_pipeline = None
        if style_pipeline is not None:
            logger.info("🧹 Clearing style pipeline to free GPU memory for TTS")
            del style_pipeline
            style_pipeline = None
    elif keep_pipeline == "style":
        if timbre_pipeline is not None:
            logger.info("🧹 Clearing timbre pipeline to free GPU memory for style transfer")
            del timbre_pipeline
            timbre_pipeline = None
        if tts_pipeline is not None:
            logger.info("🧹 Clearing TTS pipeline to free GPU memory for style transfer")
            del tts_pipeline
            tts_pipeline = None

    torch.cuda.empty_cache()
    gc.collect()
    logger.info("🧹 GPU cache cleared and garbage collection completed")


def vevo_timbre(content_wav_path, reference_wav_path, output_path):
    logger.info(f"🎵 Starting timbre transfer: {content_wav_path} -> {output_path}")
    logger.info(f"🎯 Using reference audio: {reference_wav_path}")

    # Clear TTS pipeline to free memory for timbre transfer
    clear_unused_pipeline("timbre")

    pipeline = get_timbre_pipeline()
    logger.info("🔧 Running timbre transfer inference with flow matching...")
    gen_audio = pipeline.inference_fm(
        src_wav_path=content_wav_path,
        timbre_ref_wav_path=reference_wav_path,
        flow_matching_steps=32,
    )
    save_audio(gen_audio, output_path=output_path)
    logger.info(f"✅ Timbre transfer completed: {output_path}")


def vevo_style(content_wav_path, style_wav_path, output_path):
    logger.info(f"🎨 Starting style transfer: {content_wav_path} -> {output_path}")
    logger.info(f"🎯 Using style reference: {style_wav_path}")

    # Clear other pipelines to free memory for style transfer
    clear_unused_pipeline("style")

    pipeline = get_style_pipeline()
    logger.info("🔧 Running style transfer inference with autoregressive and flow matching...")
    gen_audio = pipeline.inference_ar_and_fm(
        src_wav_path=content_wav_path,
        src_text=None,
        style_ref_wav_path=style_wav_path,
        timbre_ref_wav_path=content_wav_path,
    )
    save_audio(gen_audio, output_path=output_path)
    logger.info(f"✅ Style transfer completed: {output_path}")


def vevo_tts(
    src_text,
    ref_wav_path,
    timbre_ref_wav_path=None,
    output_path=None,
    ref_text=None,
    src_language="en",
    ref_language="en",
):
    logger.info(f"🗣️ Starting TTS synthesis: '{src_text[:50]}...' -> {output_path}")
    logger.info(f"🎯 Style reference: {ref_wav_path}")
    logger.info(f"🎵 Timbre reference: {timbre_ref_wav_path}")
    logger.info(f"🌍 Languages - Source: {src_language}, Reference: {ref_language}")

    # Clear timbre pipeline to free memory for TTS
    clear_unused_pipeline("tts")

    if timbre_ref_wav_path is None:
        timbre_ref_wav_path = ref_wav_path
        logger.info("🔄 Using style reference as timbre reference")

    pipeline = get_tts_pipeline()
    logger.info("🔧 Running TTS inference with autoregressive and flow matching...")
    gen_audio = pipeline.inference_ar_and_fm(
        src_wav_path=None,
        src_text=src_text,
        style_ref_wav_path=ref_wav_path,
        timbre_ref_wav_path=timbre_ref_wav_path,
        style_ref_wav_text=ref_text,
        src_text_language=src_language,
        style_ref_wav_text_language=ref_language,
    )

    assert output_path is not None
    save_audio(gen_audio, output_path=output_path)
    logger.info(f"✅ TTS synthesis completed: {output_path}")


from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Literal
from fastapi.responses import StreamingResponse
import numpy as np

# Allowed voices: two American English (male/female) and two British English (male/female)
VoiceOption = Literal["am_michael", "af_nicole", "bm_george", "bf_emma"]

pipeline = KPipeline(lang_code="a")  # 'a' for English


def kokoro_tts_chunked_segments(
    text: str,
    voice: str = "am_michael",
    speed: float = 1.0,
    split_pattern: str = r"\n+",
    chunk_duration_sec: int = 6,
    sample_rate: int = 24000,
    cleanup_full_chunks: bool = True
) -> list:
    """
    Generate TTS audio chunks from long text using Kokoro,
    split each chunk into 6-second segments,
    and return a list of individual segment file paths.

    Parameters:
    - text: Input long multi-paragraph text.
    - voice: Kokoro voice name.
    - speed: Speech speed multiplier.
    - split_pattern: Regex pattern to split input text into chunks.
    - chunk_duration_sec: Duration (seconds) for splitting audio chunks.
    - sample_rate: Sample rate for saving WAV files.
    - cleanup_full_chunks: Remove intermediate full chunk WAV files.

    Returns:
    - List of paths to individual 6-second segment files.
    """
    logger.info(f"🎤 Starting Kokoro TTS chunked generation for text: '{text[:100]}...'")
    logger.info(f"🎵 Voice: {voice}, Speed: {speed}, Chunk duration: {chunk_duration_sec}s")

    kokoro_pipeline = KPipeline(lang_code="a")
    generator = kokoro_pipeline(text, voice=voice, speed=speed, split_pattern=split_pattern)

    segment_files = []
    temp_files_to_cleanup = []

    try:
        for i, (_, _, audio_np) in enumerate(generator):
            # Save the full chunk temporarily
            with NamedTemporaryFile(delete=False, suffix=".wav") as full_temp:
                full_wav_path = full_temp.name
                temp_files_to_cleanup.append(full_wav_path)

            sf.write(full_wav_path, audio_np, sample_rate)
            logger.info(f"📝 Generated chunk {i}: {full_wav_path}")

            # Load and split into smaller segments
            audio = AudioSegment.from_wav(full_wav_path)
            duration_ms = len(audio)
            chunk_len_ms = chunk_duration_sec * 1000
            num_chunks = math.ceil(duration_ms / chunk_len_ms)

            logger.info(f"🔪 Splitting chunk {i} into {num_chunks} segments of {chunk_duration_sec}s each")

            for j in range(num_chunks):
                start = j * chunk_len_ms
                end = min((j + 1) * chunk_len_ms, duration_ms)
                chunk = audio[start:end]

                # Save individual segment
                with NamedTemporaryFile(delete=False, suffix=".wav") as segment_temp:
                    segment_path = segment_temp.name

                chunk.export(segment_path, format="wav")
                segment_files.append(segment_path)

                logger.info(f"✅ Saved segment {i}_{j}: {segment_path} ({len(chunk)}ms)")

        logger.info(f"🔗 Generated {len(segment_files)} total segments")
        return segment_files

    finally:
        # Clean up temporary full chunk files
        if cleanup_full_chunks:
            for temp_file in temp_files_to_cleanup:
                try:
                    os.remove(temp_file)
                    logger.info(f"🧹 Cleaned up temporary file: {temp_file}")
                except OSError as e:
                    logger.warning(f"⚠️ Could not clean up temporary file {temp_file}: {e}")


def concatenate_audio_chunks(chunk_paths: list, output_path: str, sample_rate: int = 24000):
    """
    Concatenate multiple audio chunk files into a single audio file.

    Parameters:
    - chunk_paths: List of paths to audio chunk files
    - output_path: Path for the output concatenated audio file
    - sample_rate: Sample rate for the output file
    """
    logger.info(f"🔗 Concatenating {len(chunk_paths)} audio chunks...")

    if not chunk_paths:
        raise ValueError("No audio chunks provided for concatenation")

    # Load all chunks and concatenate
    all_audio_segments = []

    for i, chunk_path in enumerate(chunk_paths):
        try:
            # Load chunk as numpy array
            audio_data, sr = sf.read(chunk_path)

            # Ensure mono audio
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)

            # Resample if necessary
            if sr != sample_rate:
                import librosa
                audio_data = librosa.resample(audio_data, orig_sr=sr, target_sr=sample_rate)

            all_audio_segments.append(audio_data)
            logger.info(f"✅ Loaded chunk {i+1}: {len(audio_data)} samples")

        except Exception as e:
            logger.error(f"❌ Error loading chunk {chunk_path}: {e}")
            raise e

    # Concatenate all segments
    if all_audio_segments:
        final_audio = np.concatenate(all_audio_segments, axis=0)
        logger.info(f"🔗 Concatenated into final audio: {len(final_audio)} samples")

        # Save final audio
        sf.write(output_path, final_audio, sample_rate)
        logger.info(f"💾 Final concatenated audio saved: {output_path}")
    else:
        raise ValueError("No valid audio segments to concatenate")


def kokoro_tts_chunked(
    text: str,
    voice: str = "am_michael",
    speed: float = 1.0,
    split_pattern: str = r"\n+",
    chunk_duration_sec: int = 6,
    sample_rate: int = 24000,
    cleanup_full_chunks: bool = True
) -> str:
    """
    Generate TTS audio chunks from long text using Kokoro,
    split each chunk into 6-second segments,
    concatenate them into a single audio file,
    and return the final audio file path.

    Parameters:
    - text: Input long multi-paragraph text.
    - voice: Kokoro voice name.
    - speed: Speech speed multiplier.
    - split_pattern: Regex pattern to split input text into chunks.
    - chunk_duration_sec: Duration (seconds) for splitting audio chunks.
    - sample_rate: Sample rate for saving WAV files.
    - cleanup_full_chunks: Remove intermediate full chunk WAV files.

    Returns:
    - Path to the final concatenated audio file (str).
    """
    logger.info(f"🎤 Starting Kokoro TTS chunked generation for text: '{text[:100]}...'")
    logger.info(f"🎵 Voice: {voice}, Speed: {speed}, Chunk duration: {chunk_duration_sec}s")

    kokoro_pipeline = KPipeline(lang_code="a")
    generator = kokoro_pipeline(text, voice=voice, speed=speed, split_pattern=split_pattern)

    all_audio_segments = []
    temp_files_to_cleanup = []

    try:
        for i, (_, _, audio_np) in enumerate(generator):
            # Save the full chunk temporarily
            with NamedTemporaryFile(delete=False, suffix=".wav") as full_temp:
                full_wav_path = full_temp.name
                temp_files_to_cleanup.append(full_wav_path)

            sf.write(full_wav_path, audio_np, sample_rate)
            logger.info(f"📝 Generated chunk {i}: {full_wav_path}")

            # Load and split into smaller segments
            audio = AudioSegment.from_wav(full_wav_path)
            duration_ms = len(audio)
            chunk_len_ms = chunk_duration_sec * 1000
            num_chunks = math.ceil(duration_ms / chunk_len_ms)

            logger.info(f"🔪 Splitting chunk {i} into {num_chunks} segments of {chunk_duration_sec}s each")

            for j in range(num_chunks):
                start = j * chunk_len_ms
                end = min((j + 1) * chunk_len_ms, duration_ms)
                chunk = audio[start:end]

                # Convert to numpy array and add to collection
                chunk_samples = np.array(chunk.get_array_of_samples(), dtype=np.float32)
                if chunk.channels == 2:
                    chunk_samples = chunk_samples.reshape((-1, 2)).mean(axis=1)
                chunk_samples = chunk_samples / (2**15)  # Normalize from int16 to float32
                all_audio_segments.append(chunk_samples)

                logger.info(f"✅ Processed segment {i}_{j}: {len(chunk_samples)} samples")

        # Concatenate all segments
        if all_audio_segments:
            final_audio = np.concatenate(all_audio_segments, axis=0)
            logger.info(f"🔗 Concatenated {len(all_audio_segments)} segments into final audio: {len(final_audio)} samples")

            # Save final audio
            with NamedTemporaryFile(delete=False, suffix=".wav") as final_temp:
                final_audio_path = final_temp.name

            sf.write(final_audio_path, final_audio, sample_rate)
            logger.info(f"💾 Final audio saved: {final_audio_path}")

            return final_audio_path
        else:
            raise ValueError("No audio segments were generated")

    finally:
        # Clean up temporary files
        if cleanup_full_chunks:
            for temp_file in temp_files_to_cleanup:
                try:
                    os.remove(temp_file)
                    logger.info(f"🧹 Cleaned up temporary file: {temp_file}")
                except OSError as e:
                    logger.warning(f"⚠️ Could not clean up temporary file {temp_file}: {e}")


@app.post("/tts")
async def kokoro_tts(
    text: str = Form(..., description="Text to synthesize"),
    voice: VoiceOption = Form("am_michael", description="Voice option")
):
    if not text.strip():
        raise HTTPException(status_code=400, detail="Text cannot be empty")

    try:
        gen = pipeline(text, voice=voice)
        audio_chunks = [chunk for _, _, chunk in gen]

        # Concatenate all audio chunks into one numpy array
        audio_data = np.concatenate(audio_chunks, axis=0)

        buf = io.BytesIO()
        sf.write(buf, audio_data, samplerate=24000, format="WAV")
        buf.seek(0)

        headers = {
            "Content-Disposition": 'attachment; filename="tts_output.wav"'
        }

        return StreamingResponse(buf, media_type="audio/wav", headers=headers)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {e}")
@app.post("/timbre-transfer/")
async def timbre_transfer(
    source_audio: UploadFile = File(...),
    reference_audio: UploadFile = File(...),
    noise_cancellation: bool = Form(False),
    normalize_audio_flag: bool = Form(False),
):
    logger.info("🚀 Starting timbre transfer request")
    logger.info(f"📁 Source audio: {source_audio.filename} ({source_audio.content_type})")
    logger.info(f"📁 Reference audio: {reference_audio.filename} ({reference_audio.content_type})")
    logger.info(f"🔊 Noise cancellation: {noise_cancellation}")
    logger.info(f"🔧 Audio normalization: {normalize_audio_flag}")

    with NamedTemporaryFile(delete=False, suffix=".wav") as src_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as ref_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as out_temp:

        logger.info("💾 Saving uploaded files to temporary locations...")
        shutil.copyfileobj(source_audio.file, src_temp)
        shutil.copyfileobj(reference_audio.file, ref_temp)

        src_path = src_temp.name
        ref_path = ref_temp.name
        out_path = out_temp.name
        logger.info(f"📂 Temp files: src={src_path}, ref={ref_path}, out={out_path}")

    # Apply noise cancellation first if requested (both audios)
    if noise_cancellation:
        logger.info("🔊 Noise cancellation requested for source audio")
        with NamedTemporaryFile(delete=False, suffix=".wav") as denoised_src_temp:
            denoised_src_path = denoised_src_temp.name
        apply_noise_cancellation(src_path, denoised_src_path)
        os.remove(src_path)
        src_path = denoised_src_path
        logger.info(f"✅ Noise cancellation applied to source audio: {src_path}")

        logger.info("🔊 Noise cancellation requested for reference audio")
        with NamedTemporaryFile(delete=False, suffix=".wav") as denoised_ref_temp:
            denoised_ref_path = denoised_ref_temp.name
        apply_noise_cancellation(ref_path, denoised_ref_path)
        os.remove(ref_path)
        ref_path = denoised_ref_path
        logger.info(f"✅ Noise cancellation applied to reference audio: {ref_path}")

    # Apply normalization next if requested (both audios)
    if normalize_audio_flag:
        logger.info("🔧 Audio normalization requested for source audio")
        with NamedTemporaryFile(delete=False, suffix=".wav") as normalized_src_temp:
            normalized_src_path = normalized_src_temp.name
        normalize_audio(src_path, normalized_src_path)
        os.remove(src_path)
        src_path = normalized_src_path
        logger.info(f"✅ Source audio normalized: {src_path}")

        logger.info("🔧 Audio normalization requested for reference audio")
        with NamedTemporaryFile(delete=False, suffix=".wav") as normalized_ref_temp:
            normalized_ref_path = normalized_ref_temp.name
        normalize_audio(ref_path, normalized_ref_path)
        os.remove(ref_path)
        ref_path = normalized_ref_path
        logger.info(f"✅ Reference audio normalized: {ref_path}")

    start_time = time.time()
    logger.info("⏱️ Starting timbre transfer inference...")
    # Run inference
    vevo_timbre(src_path, ref_path, out_path)
    end_time = time.time()
    latency = end_time - start_time
    logger.info(f"⏱️ Timbre transfer completed in {latency:.2f} seconds")

    response = FileResponse(out_path, media_type="audio/wav", filename="output.wav")
    response.headers["X-Inference-Latency"] = str(latency)

    # Clean up temporary files
    logger.info("🧹 Cleaning up temporary files...")
    try:
        os.remove(src_path)
        os.remove(ref_path)
        logger.info("✅ Temporary files cleaned up successfully")
    except OSError as e:
        logger.warning(f"⚠️ Could not clean up some temporary files: {e}")

    logger.info("🎉 Timbre transfer request completed successfully")
    return response


@app.post("/style-transfer/")
async def style_transfer(
    source_audio: UploadFile = File(...),
    style_reference_audio: UploadFile = File(...),
):
    logger.info("🚀 Starting style transfer request")
    logger.info(f"📁 Source audio: {source_audio.filename} ({source_audio.content_type})")
    logger.info(f"📁 Style reference audio: {style_reference_audio.filename} ({style_reference_audio.content_type})")

    with NamedTemporaryFile(delete=False, suffix=".wav") as src_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as style_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as out_temp:

        logger.info("💾 Saving uploaded files to temporary locations...")
        shutil.copyfileobj(source_audio.file, src_temp)
        shutil.copyfileobj(style_reference_audio.file, style_temp)

        src_path = src_temp.name
        style_path = style_temp.name
        out_path = out_temp.name
        logger.info(f"📂 Temp files: src={src_path}, style={style_path}, out={out_path}")

    start_time = time.time()
    logger.info("⏱️ Starting style transfer inference...")
    # Run inference
    vevo_style(src_path, style_path, out_path)
    end_time = time.time()
    latency = end_time - start_time
    logger.info(f"⏱️ Style transfer completed in {latency:.2f} seconds")

    response = FileResponse(out_path, media_type="audio/wav", filename="style_output.wav")
    response.headers["X-Inference-Latency"] = str(latency)

    # Clean up temporary files
    logger.info("🧹 Cleaning up temporary files...")
    try:
        os.remove(src_path)
        os.remove(style_path)
        logger.info("✅ Temporary files cleaned up successfully")
    except OSError as e:
        logger.warning(f"⚠️ Could not clean up some temporary files: {e}")

    logger.info("🎉 Style transfer request completed successfully")
    return response


@app.post("/text-to-speech/")
async def text_to_speech(
    text: str = Form(...),
    reference_audio: UploadFile = File(...),
    reference_text: str = Form(None),
    timbre_audio: UploadFile = File(None),
    src_language: str = Form("en"),
    ref_language: str = Form("en"),
    noise_cancellation: bool = Form(False),
    chatterbox: bool = Form(False),
):
    logger.info("🚀 Starting text-to-speech request")
    logger.info(f"📝 Text: '{text[:100]}...' (length: {len(text)})")
    logger.info(f"📁 Reference audio: {reference_audio.filename} ({reference_audio.content_type})")
    logger.info(f"📝 Reference text: {reference_text}")
    logger.info(f"📁 Timbre audio: {timbre_audio.filename if timbre_audio else 'None'}")
    logger.info(f"🌍 Languages - Source: {src_language}, Reference: {ref_language}")
    logger.info(f"🔊 Noise cancellation: {noise_cancellation}")
    logger.info(f"🗣️ Chatterbox: {chatterbox}")

    with NamedTemporaryFile(delete=False, suffix=".wav") as ref_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as out_temp:

        # Save reference audio
        logger.info("💾 Saving reference audio to temporary file...")
        shutil.copyfileobj(reference_audio.file, ref_temp)
        ref_path = ref_temp.name
        out_path = out_temp.name
        logger.info(f"📂 Reference audio saved: {ref_path}")

        # Handle optional timbre audio
        timbre_path = None
        if timbre_audio:
            logger.info("💾 Processing timbre audio...")
            with NamedTemporaryFile(delete=False, suffix=".wav") as timbre_temp:
                shutil.copyfileobj(timbre_audio.file, timbre_temp)
                timbre_path = timbre_temp.name
                logger.info(f"📂 Timbre audio saved: {timbre_path}")

                # Apply noise cancellation if requested
                if noise_cancellation:
                    logger.info("🔊 Noise cancellation requested for timbre audio")
                    with NamedTemporaryFile(delete=False, suffix=".wav") as denoised_temp:
                        denoised_path = denoised_temp.name
                    apply_noise_cancellation(timbre_path, denoised_path)
                    # Clean up original file and use denoised version
                    os.remove(timbre_path)
                    timbre_path = denoised_path
                    logger.info(f"✅ Noise cancellation applied, using: {timbre_path}")

                # Apply Chatterbox processing if requested
                if chatterbox:
                    logger.info("🗣️ Chatterbox processing requested for timbre audio")
                    logger.info("📞 Calling Chatterbox inference API")
                    with NamedTemporaryFile(delete=False, suffix=".wav") as chatterbox_temp:
                        chatterbox_path = chatterbox_temp.name

                    # Prepare files and data for Chatterbox API
                    files = {"ref_audio": open(timbre_path, "rb")}
                    data = {
                        "text": reference_text or "",  # Use reference_text as generation_text
                        "exaggeration": 0.2,
                        "cfg_weight": 0.2,
                        "temperature": 0.5,
                        "seed": 0
                    }
                    logger.info(f"📊 Chatterbox parameters: {data}")
                    logger.info(f"🌐 Chatterbox API URL: {EXISTING_API_URL_CH}/synthesize/")

                    try:
                        logger.info("⏳ Sending request to Chatterbox API...")
                        async with httpx.AsyncClient() as client:
                            response = await client.post(
                                f"{EXISTING_API_URL_CH}/synthesize/",
                                files=files,
                                data=data,
                                timeout=TIMEOUT
                            )
                            response.raise_for_status()
                            logger.info(f"✅ Chatterbox API responded with status: {response.status_code}")

                            # Save Chatterbox output
                            logger.info(f"💾 Saving Chatterbox output to: {chatterbox_path}")
                            with open(chatterbox_path, "wb") as f:
                                f.write(response.content)

                            # Clean up original timbre file and use Chatterbox output
                            files["ref_audio"].close()
                            os.remove(timbre_path)
                            timbre_path = chatterbox_path
                            logger.info(f"✅ Chatterbox processing completed, using: {timbre_path}")

                    except Exception as e:
                        files["ref_audio"].close()
                        logger.error(f"❌ Chatterbox API error: {e}")
                        logger.warning("⚠️ Continuing with original timbre audio due to Chatterbox failure")
                        # Continue with original timbre_path if Chatterbox fails
                        try:
                            os.remove(chatterbox_path)
                        except OSError:
                            pass

    start_time = time.time()
    logger.info("⏱️ Starting TTS inference...")
    # Run TTS inference
    vevo_tts(
        src_text=text,
        ref_wav_path=ref_path,
        timbre_ref_wav_path=timbre_path,
        output_path=out_path,
        ref_text=reference_text,
        src_language=src_language,
        ref_language=ref_language,
    )
    end_time = time.time()
    latency = end_time - start_time
    logger.info(f"⏱️ TTS inference completed in {latency:.2f} seconds")

    response = FileResponse(out_path, media_type="audio/wav", filename="tts_output.wav")
    response.headers["X-Inference-Latency"] = str(latency)
    logger.info(f"📤 Sending response with latency header: {latency:.2f}s")

    # Clean up temporary files
    logger.info("🧹 Cleaning up temporary files...")
    try:
        os.remove(ref_path)
        if timbre_path:
            os.remove(timbre_path)
        logger.info("✅ Temporary files cleaned up successfully")
    except OSError as e:
        logger.warning(f"⚠️ Could not clean up some temporary files: {e}")

    logger.info("🎉 Text-to-speech request completed successfully")
    return response


@app.post("/combined-pipeline/")
async def combined_pipeline(
    text: str = Form(..., description="Text to be converted to speech using Kokoro TTS"),
    voice: VoiceOption = Form("am_michael", description="Voice model for Kokoro TTS generation"),
    style_transfer_audio: UploadFile = File(None, description="Optional reference audio file for style transfer processing"),
    user_audio: UploadFile = File(..., description="Reference audio file for timbre transfer"),
    noise_cancellation: bool = Form(False, description="Apply noise cancellation to reference audio"),
    normalization: bool = Form(False, description="Apply audio normalization as preprocessing"),
):
    """
    Combined API endpoint that integrates Kokoro TTS, style transfer, and timbre transfer in a sequential pipeline.

    Processing Flow:
    1. Generate speech from input text using Kokoro TTS with 6-second audio chunks
    2. If style_transfer_audio is provided, apply style transfer using Kokoro audio as source
    3. Apply timbre transfer using the output from step 2 and user_audio as reference
    4. Apply optional noise cancellation and normalization as specified

    Returns the final processed audio after the complete pipeline.
    """
    logger.info("🚀 Starting combined pipeline request")
    logger.info(f"📝 Text: '{text[:100]}...' (length: {len(text)})")
    logger.info(f"🎵 Voice: {voice}")
    logger.info(f"🎨 Style transfer audio: {style_transfer_audio.filename if style_transfer_audio else 'None'}")
    logger.info(f"👤 User audio: {user_audio.filename}")
    logger.info(f"🔊 Noise cancellation: {noise_cancellation}")
    logger.info(f"🔧 Normalization: {normalization}")

    if not text.strip():
        raise HTTPException(status_code=400, detail="Text cannot be empty")

    start_time = time.time()

    try:
        # Step 1: Generate speech using Kokoro TTS with chunking
        logger.info("🎤 Step 1: Generating speech using Kokoro TTS with chunking...")
        kokoro_chunks = kokoro_tts_chunked_segments(
            text=text,
            voice=voice,
            speed=1.0,
            chunk_duration_sec=6
        )
        logger.info(f"✅ Kokoro TTS completed: Generated {len(kokoro_chunks)} chunks")

        # Prepare reference audio for processing
        logger.info("📁 Preparing reference audio...")

        # Save user audio to temporary file
        with NamedTemporaryFile(delete=False, suffix=".wav") as user_temp:
            shutil.copyfileobj(user_audio.file, user_temp)
            user_path = user_temp.name

        # Apply noise cancellation to reference audio if requested
        if noise_cancellation:
            logger.info("🔊 Applying noise cancellation to reference audio...")
            with NamedTemporaryFile(delete=False, suffix=".wav") as denoised_temp:
                denoised_user_path = denoised_temp.name
            apply_noise_cancellation(user_path, denoised_user_path)
            os.remove(user_path)
            user_path = denoised_user_path
            logger.info(f"✅ Noise cancellation applied to reference audio: {user_path}")

        # Normalize reference audio if requested
        if normalization:
            logger.info("🔧 Normalizing reference audio...")
            with NamedTemporaryFile(delete=False, suffix=".wav") as normalized_ref_temp:
                normalized_ref_path = normalized_ref_temp.name
            normalize_audio(user_path, normalized_ref_path)
            os.remove(user_path)
            user_path = normalized_ref_path
            logger.info(f"✅ Reference audio normalized: {user_path}")

        # Prepare style reference audio if provided
        style_path = None
        if style_transfer_audio:
            logger.info("� Preparing style reference audio...")
            with NamedTemporaryFile(delete=False, suffix=".wav") as style_temp:
                shutil.copyfileobj(style_transfer_audio.file, style_temp)
                style_path = style_temp.name
            logger.info(f"✅ Style reference audio prepared: {style_path}")

        # Process each chunk through the pipeline
        processed_chunks = []
        temp_files_to_cleanup = []

        try:
            for i, chunk_path in enumerate(kokoro_chunks):
                logger.info(f"� Processing chunk {i+1}/{len(kokoro_chunks)}")
                current_chunk_path = chunk_path
                temp_files_to_cleanup.append(chunk_path)

                # Step 2: Apply style transfer to this chunk if style_transfer_audio is provided
                if style_path:
                    logger.info(f"🎨 Applying style transfer to chunk {i+1}...")

                    # Normalize source chunk if requested
                    if normalization:
                        with NamedTemporaryFile(delete=False, suffix=".wav") as normalized_chunk_temp:
                            normalized_chunk_path = normalized_chunk_temp.name
                        normalize_audio(current_chunk_path, normalized_chunk_path)
                        temp_files_to_cleanup.append(current_chunk_path)
                        current_chunk_path = normalized_chunk_path
                        temp_files_to_cleanup.append(current_chunk_path)

                    # Apply style transfer to this chunk
                    with NamedTemporaryFile(delete=False, suffix=".wav") as style_out_temp:
                        style_out_path = style_out_temp.name

                    # Clear GPU memory and other pipelines before style transfer
                    clear_gpu_memory()
                    clear_unused_pipeline("style")

                    vevo_style(current_chunk_path, style_path, style_out_path)
                    temp_files_to_cleanup.append(current_chunk_path)
                    current_chunk_path = style_out_path
                    temp_files_to_cleanup.append(current_chunk_path)

                    # Clear GPU memory after style transfer
                    clear_gpu_memory()
                    logger.info(f"✅ Style transfer completed for chunk {i+1}")
                else:
                    # Just normalize source chunk if requested and no style transfer
                    if normalization:
                        logger.info(f"🔧 Normalizing source chunk {i+1}...")
                        with NamedTemporaryFile(delete=False, suffix=".wav") as normalized_chunk_temp:
                            normalized_chunk_path = normalized_chunk_temp.name
                        normalize_audio(current_chunk_path, normalized_chunk_path)
                        temp_files_to_cleanup.append(current_chunk_path)
                        current_chunk_path = normalized_chunk_path
                        temp_files_to_cleanup.append(current_chunk_path)

                # Step 3: Apply timbre transfer to this chunk
                logger.info(f"🎭 Applying timbre transfer to chunk {i+1}...")

                with NamedTemporaryFile(delete=False, suffix=".wav") as timbre_out_temp:
                    timbre_out_path = timbre_out_temp.name

                # Clear GPU memory and other pipelines before timbre transfer
                clear_gpu_cache()
                clear_unused_pipeline("timbre")

                vevo_timbre(current_chunk_path, user_path, timbre_out_path)
                temp_files_to_cleanup.append(current_chunk_path)

                # Clear GPU memory after timbre transfer
                clear_gpu_cache()

                # Add processed chunk to collection
                processed_chunks.append(timbre_out_path)
                temp_files_to_cleanup.append(timbre_out_path)

                logger.info(f"✅ Timbre transfer completed for chunk {i+1}")

        except Exception as e:
            logger.error(f"❌ Error processing chunks: {e}")
            # Clean up temporary files before re-raising
            for temp_file in temp_files_to_cleanup:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except OSError:
                    pass
            raise e

        # Step 4: Concatenate all processed chunks
        logger.info(f"🔗 Concatenating {len(processed_chunks)} processed chunks...")

        # Create output file for final result
        with NamedTemporaryFile(delete=False, suffix=".wav") as final_temp:
            final_output_path = final_temp.name

        # Concatenate all processed chunks
        concatenate_audio_chunks(processed_chunks, final_output_path)

        end_time = time.time()
        total_latency = end_time - start_time
        logger.info(f"⏱️ Combined pipeline completed in {total_latency:.2f} seconds")

        # Prepare response
        response = FileResponse(final_output_path, media_type="audio/wav", filename="combined_pipeline_output.wav")
        response.headers["X-Inference-Latency"] = str(total_latency)

        # Clean up temporary files
        logger.info("🧹 Cleaning up temporary files...")
        try:
            # Clean up reference audio files
            if os.path.exists(user_path):
                os.remove(user_path)
            if style_path and os.path.exists(style_path):
                os.remove(style_path)

            # Clean up all temporary chunk files
            for temp_file in temp_files_to_cleanup:
                if os.path.exists(temp_file):
                    os.remove(temp_file)

            logger.info("✅ Temporary files cleaned up successfully")
        except OSError as e:
            logger.warning(f"⚠️ Could not clean up some temporary files: {e}")

        logger.info("🎉 Combined pipeline request completed successfully")
        return response

    except Exception as e:
        logger.error(f"Combined pipeline failed: {e}")
        raise HTTPException(status_code=500, detail=f"Combined pipeline failed: {e}")
