

from kokoro import K<PERSON>ipeline
from pydub import AudioSegment
import soundfile as sf
import math
from IPython.display import Audio, display
import os

def kokoro_tts_chunked_only(
    text: str,
    voice: str = "af_heart",
    speed: float = 1.0,
    split_pattern: str = r"\n+",
    chunk_duration_sec: int = 6,
    sample_rate: int = 24000,
    cleanup_full_chunks: bool = True
) -> list[str]:
    """
    Generate TTS audio chunks from long text using Kokoro,
    split each chunk into 6-second segments,
    save them as individual WAV files,
    and return the list of chunk file paths.
    
    Parameters:
    - text: Input long multi-paragraph text.
    - voice: Kokoro voice name.
    - speed: Speech speed multiplier.
    - split_pattern: Regex pattern to split input text into chunks.
    - chunk_duration_sec: Duration (seconds) for splitting audio chunks.
    - sample_rate: Sample rate for saving WAV files.
    - cleanup_full_chunks: Remove intermediate full chunk WAV files.
    
    Returns:
    - List of chunk file paths (str).
    """
    pipeline = KPipeline(lang_code="a")
    generator = pipeline(text, voice=voice, speed=speed, split_pattern=split_pattern)
    
    six_sec_paths = []
    
    for i, (gs, ps, audio_np) in enumerate(generator):
        full_wav_path = f"{i}_full.wav"
        sf.write(full_wav_path, audio_np, sample_rate)
        
        audio = AudioSegment.from_wav(full_wav_path)
        duration_ms = len(audio)
        chunk_len_ms = chunk_duration_sec * 1000
        num_chunks = math.ceil(duration_ms / chunk_len_ms)
        
        for j in range(num_chunks):
            start = j * chunk_len_ms
            end = min((j + 1) * chunk_len_ms, duration_ms)
            chunk = audio[start:end]
            chunk_path = f"{i}_{j}.wav"
            chunk.export(chunk_path, format="wav")
            six_sec_paths.append(chunk_path)
            # Optionally display each chunk audio in Jupyter, comment out if not needed
            # display(Audio(chunk_path))
        
        if cleanup_full_chunks:
            os.remove(full_wav_path)
    
    return six_sec_paths


